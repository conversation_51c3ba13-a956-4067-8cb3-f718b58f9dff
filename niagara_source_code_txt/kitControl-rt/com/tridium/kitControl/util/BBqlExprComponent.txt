/*
 * Copyright 2010, Tridium, Inc. All Rights Reserved.
 */
package com.tridium.kitControl.util;

import javax.baja.bql.util.BExprComponent;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * BQL Expression Component
 *
 * <AUTHOR>
 * @creation 	29 Jul 2010
 * @version 	1
 * @since 		Niagara 3.6
 */
@NiagaraType
public final class BBqlExprComponent
    extends BExprComponent
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.kitControl.util.BBqlExprComponent(2979906276)1.0$ @*/
/* Generated Wed Jan 05 14:19:30 EST 2022 by Slot-o-Mat<PERSON> (c) Tridium, Inc. 2012-2022 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BBqlExprComponent.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
