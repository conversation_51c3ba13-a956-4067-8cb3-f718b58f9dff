/*
 * Copyright 2001 Tridium, Inc. All Rights Reserved.
 */
package com.tridium.kitControl.math;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;

/**
 * BAbsValue performs the operation out = abs(inA)
 * (absolute value of inA)
 *
 * <AUTHOR>
 * @creation  29 Aug 2001
 * @version   $Revision: 8$ $Date: 3/30/2004 3:41:08 PM$
 * @since     Baja 1.0
 */
@NiagaraType
public class BAbsValue
  extends BUnaryMath
{ 
  
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.tridium.kitControl.math.BAbsValue(2979906276)1.0$ @*/
/* Generated Wed Jan 05 14:19:30 EST 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BAbsValue.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ <PERSON><PERSON> BAJA AUTO GENERATED CODE -------------- +*/
  
  
  protected double calculate(double a)
  {
    return Math.abs(a);
  }

  /**
   * Get the icon.
   */
  public BIcon getIcon() { return icon; }
  private static final BIcon icon = BIcon.std("control/math/abs.png");

}
