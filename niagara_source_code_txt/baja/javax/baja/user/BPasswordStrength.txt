/**
 * Copyright 2013 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.user;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sync.BProxyComponentSpace;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BNumber;
import javax.baja.sys.BStruct;
import javax.baja.sys.BValue;
import javax.baja.sys.BajaException;
import javax.baja.sys.Context;
import javax.baja.sys.IPropertyValidator;
import javax.baja.sys.Localizable;
import javax.baja.sys.LocalizableRuntimeException;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.sys.Validatable;
import javax.baja.util.LexiconText;

import com.tridium.nre.security.PasswordStrength;

/**
 *
 * <AUTHOR> Smith
 * @creation  08 Feb 2012
 * @version   $Revision: 1$ $Date: 3/28/02 10:35:40 AM EST$
 * @since     Niagara AX 3.8
 */
@NiagaraType
/*
 minimum password length
 */
@NiagaraProperty(
  name = "minimumLength",
  type = "int",
  defaultValue = "10",
  facets = @Facet(name = "BFacets.MIN", value = "0")
)
/*
 Maximum password length
 @since Niagara 4.13
 */
@NiagaraProperty(
  name = "maximumLength",
  type = "int",
  defaultValue = "64",
  facets = @Facet(name = "BFacets.MIN", value = "1")
)
/*
 minimum lower case letters
 */
@NiagaraProperty(
  name = "minimumLowerCase",
  type = "int",
  defaultValue = "1",
  facets = @Facet(name = "BFacets.MIN", value = "0")
)
/*
 minimum upper case letters
 */
@NiagaraProperty(
  name = "minimumUpperCase",
  type = "int",
  defaultValue = "1",
  facets = @Facet(name = "BFacets.MIN", value = "0")
)
/*
 minimum numbers
 */
@NiagaraProperty(
  name = "minimumDigits",
  type = "int",
  defaultValue = "1",
  facets = @Facet(name = "BFacets.MIN", value = "0")
)
/*
 minimum numbers
 */
@NiagaraProperty(
  name = "minimumSpecial",
  type = "int",
  defaultValue = "0",
  facets = @Facet(name = "BFacets.MIN", value = "0")
)
public class BPasswordStrength
  extends BStruct
  implements IPropertyValidator
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.user.BPasswordStrength(2295610325)1.0$ @*/
/* Generated Wed Feb 08 15:23:19 EST 2023 by Slot-o-Matic (c) Tridium, Inc. 2012-2023 */

  //region Property "minimumLength"

  /**
   * Slot for the {@code minimumLength} property.
   * minimum password length
   * @see #getMinimumLength
   * @see #setMinimumLength
   */
  public static final Property minimumLength = newProperty(0, 10, BFacets.make(BFacets.MIN, 0));

  /**
   * Get the {@code minimumLength} property.
   * minimum password length
   * @see #minimumLength
   */
  public int getMinimumLength() { return getInt(minimumLength); }

  /**
   * Set the {@code minimumLength} property.
   * minimum password length
   * @see #minimumLength
   */
  public void setMinimumLength(int v) { setInt(minimumLength, v, null); }

  //endregion Property "minimumLength"

  //region Property "maximumLength"

  /**
   * Slot for the {@code maximumLength} property.
   * Maximum password length
   * @since Niagara 4.13
   * @see #getMaximumLength
   * @see #setMaximumLength
   */
  public static final Property maximumLength = newProperty(0, 64, BFacets.make(BFacets.MIN, 1));

  /**
   * Get the {@code maximumLength} property.
   * Maximum password length
   * @since Niagara 4.13
   * @see #maximumLength
   */
  public int getMaximumLength() { return getInt(maximumLength); }

  /**
   * Set the {@code maximumLength} property.
   * Maximum password length
   * @since Niagara 4.13
   * @see #maximumLength
   */
  public void setMaximumLength(int v) { setInt(maximumLength, v, null); }

  //endregion Property "maximumLength"

  //region Property "minimumLowerCase"

  /**
   * Slot for the {@code minimumLowerCase} property.
   * minimum lower case letters
   * @see #getMinimumLowerCase
   * @see #setMinimumLowerCase
   */
  public static final Property minimumLowerCase = newProperty(0, 1, BFacets.make(BFacets.MIN, 0));

  /**
   * Get the {@code minimumLowerCase} property.
   * minimum lower case letters
   * @see #minimumLowerCase
   */
  public int getMinimumLowerCase() { return getInt(minimumLowerCase); }

  /**
   * Set the {@code minimumLowerCase} property.
   * minimum lower case letters
   * @see #minimumLowerCase
   */
  public void setMinimumLowerCase(int v) { setInt(minimumLowerCase, v, null); }

  //endregion Property "minimumLowerCase"

  //region Property "minimumUpperCase"

  /**
   * Slot for the {@code minimumUpperCase} property.
   * minimum upper case letters
   * @see #getMinimumUpperCase
   * @see #setMinimumUpperCase
   */
  public static final Property minimumUpperCase = newProperty(0, 1, BFacets.make(BFacets.MIN, 0));

  /**
   * Get the {@code minimumUpperCase} property.
   * minimum upper case letters
   * @see #minimumUpperCase
   */
  public int getMinimumUpperCase() { return getInt(minimumUpperCase); }

  /**
   * Set the {@code minimumUpperCase} property.
   * minimum upper case letters
   * @see #minimumUpperCase
   */
  public void setMinimumUpperCase(int v) { setInt(minimumUpperCase, v, null); }

  //endregion Property "minimumUpperCase"

  //region Property "minimumDigits"

  /**
   * Slot for the {@code minimumDigits} property.
   * minimum numbers
   * @see #getMinimumDigits
   * @see #setMinimumDigits
   */
  public static final Property minimumDigits = newProperty(0, 1, BFacets.make(BFacets.MIN, 0));

  /**
   * Get the {@code minimumDigits} property.
   * minimum numbers
   * @see #minimumDigits
   */
  public int getMinimumDigits() { return getInt(minimumDigits); }

  /**
   * Set the {@code minimumDigits} property.
   * minimum numbers
   * @see #minimumDigits
   */
  public void setMinimumDigits(int v) { setInt(minimumDigits, v, null); }

  //endregion Property "minimumDigits"

  //region Property "minimumSpecial"

  /**
   * Slot for the {@code minimumSpecial} property.
   * minimum numbers
   * @see #getMinimumSpecial
   * @see #setMinimumSpecial
   */
  public static final Property minimumSpecial = newProperty(0, 0, BFacets.make(BFacets.MIN, 0));

  /**
   * Get the {@code minimumSpecial} property.
   * minimum numbers
   * @see #minimumSpecial
   */
  public int getMinimumSpecial() { return getInt(minimumSpecial); }

  /**
   * Set the {@code minimumSpecial} property.
   * minimum numbers
   * @see #minimumSpecial
   */
  public void setMinimumSpecial(int v) { setInt(minimumSpecial, v, null); }

  //endregion Property "minimumSpecial"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BPasswordStrength.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  public BPasswordStrength()
  {    
  }
  
  public BPasswordStrength(int len, int lower, int upper, int digits, int special)
  {
    this(len, lower, upper, digits, special, ((BNumber)maximumLength.getDefaultValue()).getInt());
  }

  /**
   * Construct a new BPasswordStrength instance with the given parameters.
   *
   * @param minLen The minimum password length (should not exceed maxLen)
   * @param lower The minimum number of lowercase characters required for passwords
   * @param upper The minimum number of uppercase characters required for passwords
   * @param digits The minimum number of digits required for passwords
   * @param special The minimum number of special characters required for passwords
   * @param maxLen The maximum password length (should not be less than minLen and must be greater than zero)
   *
   * @throws IllegalArgumentException if the given parameters are not valid for any reason.
   * An IllegalArgumentException indicates one or more of the following was violated:
   * 1. The minimumLength, minimumLowerCase, minimumUpperCase, minimumDigits, and minimumSpecial
   *    must be greater than or equal to zero.
   * 2. The maximumLength must be greater than zero.
   * 3. The maximumLength cannot be less than the minimumLength.
   * 4. The maximumLength cannot be less than the combined minimumLowerCase, minimumUpperCase, minimumDigits, and minimumSpecial.
   *
   * @since Niagara 4.13
   */
  public BPasswordStrength(int minLen, int lower, int upper, int digits, int special, int maxLen)
  {
    if (minLen < 0)
    {
      throw new IllegalArgumentException("Minimum password length cannot be less than 0");
    }

    if (lower < 0)
    {
      throw new IllegalArgumentException("Minimum lowercase password characters cannot be less than 0");
    }

    if (upper < 0)
    {
      throw new IllegalArgumentException("Minimum uppercase password characters cannot be less than 0");
    }

    if (digits < 0)
    {
      throw new IllegalArgumentException("Minimum password digits cannot be less than 0");
    }

    if (special < 0)
    {
      throw new IllegalArgumentException("Minimum special password characters cannot be less than 0");
    }

    if (maxLen < 1)
    {
      throw new IllegalArgumentException("Maximum password length cannot be less than 1");
    }

    if (minLen > maxLen)
    {
      throw new IllegalArgumentException("Minimum password length exceeds maximum password length");
    }

    if (passwordRequirementsInvalid(minLen, maxLen, lower, upper, digits, special))
    {
      throw new IllegalArgumentException("Maximum password length cannot be less than the combined minimum lowercase, uppercase, digit, and special characters");
    }

    setMinimumLength(minLen);
    setMinimumLowerCase(lower);
    setMinimumUpperCase(upper);
    setMinimumDigits(digits);
    setMinimumSpecial(special);
    setMaximumLength(maxLen);
  }

  /**
   * Creates a new BPasswordStrength using the information from the provided {@link PasswordStrength}
   * @param passwordStrength Used to create the BPasswordStrength.
   * @since Niagara 4.5
   */
  public BPasswordStrength(PasswordStrength passwordStrength)
  {
    this(passwordStrength.getMinimumLength(), passwordStrength.getMinimumLowerCase(),
      passwordStrength.getMinimumUpperCase(), passwordStrength.getMinimumDigits(),
      passwordStrength.getMinimumSpecial(), passwordStrength.getMaximumLength());
  }

  /**
   * Construct a new BPasswordStrength instance with the same properties as the given
   * BPasswordStrength instance but replacing the maximum length with the given one.
   *
   * @param passwordStrength The instance whose properties to copy to the new instance
   * @param maxLen The maximum password length to set on the new instance (should not be
   *               less than the minimum length on the given instance and must be greater than zero)
   *
   * @since Niagara 4.13
   */
  public BPasswordStrength(BPasswordStrength passwordStrength, int maxLen)
  {
    this(passwordStrength.getMinimumLength(), passwordStrength.getMinimumLowerCase(),
      passwordStrength.getMinimumUpperCase(), passwordStrength.getMinimumDigits(),
      passwordStrength.getMinimumSpecial(), maxLen);
  }

  /**
   * Returns true if the given password meets this object's strength requirements, otherwise false.
   *
   * @since Niagara 4.1
   *
   * @param messageConsumer if the strength requirements are unmet, a localizable message will be provided
   *                        to {@link Consumer#accept(Object)}
   */
  public boolean isPasswordValid(char[] password, Consumer<Localizable> messageConsumer)
  {
    if (messageConsumer == null)
    {
      messageConsumer = context -> {};
    }

    int lowerCase = 0;
    int upperCase = 0;
    int digits = 0;
    int special = 0;

    int len = password.length;
    for (char character : password)
    {
      if (Character.isLetter(character))
      {
        if (Character.isUpperCase(character))
        {
          upperCase++;
        }
        else
        {
          lowerCase++;
        }
      }
      else if (Character.isDigit(character))
      {
        digits++;
      }
      else
      {
        special++;
      }
    }

    if (len < getMinimumLength() || len > getMaximumLength() ||
        digits < getMinimumDigits() ||
        lowerCase < getMinimumLowerCase() ||
        upperCase < getMinimumUpperCase() ||
        special < getMinimumSpecial())
    {
      List<Localizable> errors = new ArrayList<>();
      errors.add(LexiconText.toLocalizable("baja", "user.password.notStrong"));
      errors.addAll(getLocalizableRequirements());

      messageConsumer.accept(Localizable.concatenate("\n- ", errors));
      return false;
    }
    return true;
  }

  public void isPasswordValid(String password)
    throws Exception
  {
    AtomicReference<Localizable> messageRef = new AtomicReference<>();
    if (!isPasswordValid(password.toCharArray(), messageRef::set))
    {
      throw new BajaException(messageRef.get().toString(null));
    }
  }

  /**
   * This method validates that the password strength follows the minimum requirements
   * of this host. For example, a host in FIPS mode cannot have passwords short than
   * 14 characters.
   *
   * <p>Subclasses that override this method should take care to enforce these limits. Failure
   * to do so may result in unexpected errors when trying to log in, or when creating PBE keys.</p>
   *
   * @param validatable The validatable instance which contains the existing
   * and proposed state of a BComplex that needs to be validated.
   * @param context the Context for the pending set operation. Might be null.
   */
  @Override
  public void validateSet(Validatable validatable, Context context)
  {
    // First check if the minimum length changed and enforce a minimum value
    // (keep legacy code of only checking minimum length min value on server side since
    // PasswordStrength.MINIMUM_ALLOWED_LENGTH could be different between WB and remote station, but
    // other checks can happen anywhere)
    int proposedMinLen = ((BNumber)validatable.getProposedValue(minimumLength)).getInt();
    BComponent parent = getParentComponent();
    if (parent != null && !(parent.getSpace() instanceof BProxyComponentSpace))
    {
      int existingMinLen = ((BNumber)validatable.getExistingValue(minimumLength)).getInt();
      if (proposedMinLen != existingMinLen &&
          proposedMinLen < PasswordStrength.MINIMUM_ALLOWED_LENGTH)
      {
        throw new LocalizableRuntimeException("baja", "passwordStrength.notEnoughCharacters",
          new Object[]{ PasswordStrength.MINIMUM_ALLOWED_LENGTH });
      }
    }

    // Check if the maximum length changed and enforce a minimum value (1) for it
    int proposedMaxLen = ((BNumber)validatable.getProposedValue(maximumLength)).getInt();
    int existingMaxLen = ((BNumber)validatable.getExistingValue(maximumLength)).getInt();
    if (proposedMaxLen != existingMaxLen && proposedMaxLen < 1)
    {
      throw new LocalizableRuntimeException("baja", "passwordStrength.invalidCharacterRequirements");
    }

    // Finally check that the maximum password length cannot be less than the minimum password
    // length and that it cannot be less than the combined minimum lowercase, uppercase, digit,
    // and special characters required.
    int lower = ((BNumber)validatable.getProposedValue(minimumLowerCase)).getInt();
    int upper = ((BNumber)validatable.getProposedValue(minimumUpperCase)).getInt();
    int digits = ((BNumber)validatable.getProposedValue(minimumDigits)).getInt();
    int special = ((BNumber)validatable.getProposedValue(minimumSpecial)).getInt();
    if (passwordRequirementsInvalid(proposedMinLen, proposedMaxLen, lower, upper, digits, special))
    {
      throw new LocalizableRuntimeException("baja", "passwordStrength.invalidCharacterRequirements");
    }
  }

  /**
   * This method validates that the password strength follows the minimum requirements
   * of this host. For example, a host in FIPS mode cannot have passwords short than
   * 14 characters.
   *
   * <p>Subclasses that override this method should take care to enforce these limits. Failure
   * to do so may result in unexpected errors when trying to log in, or when creating PBE keys.</p>
   *
   * @param instance The BComplex instance for which there is
   * a pending set operation requiring validation.
   * @param property The property on the BComplex instance for
   * which there is a pending set operation.
   * @param newValue The pending new value for the property on the
   * BComplex instance which should be validated prior to commit.
   * @param context the Context for the pending set operation. Might be null.
   */
  @Override
  public void validateSet(BComplex instance, Property property, BValue newValue, Context context)
  {
    // First check if the minimum length changed and enforce a minimum value
    // (keep legacy code of only checking minimum length min value on server side since
    // PasswordStrength.MINIMUM_ALLOWED_LENGTH could be different between WB and remote station, but
    // other checks can happen anywhere)
    boolean isMinimumChange = minimumLength.equals(property);
    int minLength;
    if (isMinimumChange)
    {
      minLength = ((BNumber)newValue).getInt();
      BComponent parent = getParentComponent();
      if (parent != null && !(parent.getSpace() instanceof BProxyComponentSpace) &&
          minLength < PasswordStrength.MINIMUM_ALLOWED_LENGTH)
      {
        throw new LocalizableRuntimeException("baja", "passwordStrength.notEnoughCharacters", new Object[]{ PasswordStrength.MINIMUM_ALLOWED_LENGTH });
      }
    }
    else
    {
      minLength = getMinimumLength();
    }

    // Check if the maximum length changed and enforce a minimum value (1) for it
    boolean isMaximumChange = !isMinimumChange && maximumLength.equals(property);
    int maxLength = isMaximumChange ? ((BNumber)newValue).getInt() : getMaximumLength();
    if (isMaximumChange && maxLength < 1)
    {
      throw new LocalizableRuntimeException("baja", "passwordStrength.invalidCharacterRequirements");
    }

    // Finally check that the maximum password length cannot be less than the minimum password
    // length and that it cannot be less than the combined minimum lowercase, uppercase, digit,
    // and special characters required.
    int lower = minimumLowerCase.equals(property) ? ((BNumber)newValue).getInt() : getMinimumLowerCase();
    int upper = minimumUpperCase.equals(property) ? ((BNumber)newValue).getInt() : getMinimumUpperCase();
    int digits = minimumDigits.equals(property) ? ((BNumber)newValue).getInt() : getMinimumDigits();
    int special = minimumSpecial.equals(property) ? ((BNumber)newValue).getInt() : getMinimumSpecial();
    if (passwordRequirementsInvalid(minLength, maxLength, lower, upper, digits, special))
    {
      throw new LocalizableRuntimeException("baja", "passwordStrength.invalidCharacterRequirements");
    }
  }

  @Override
  public IPropertyValidator getPropertyValidator(Property[] properties, Context context)
  {
    return this;
  }

  @Override
  public IPropertyValidator getPropertyValidator(Property property, Context context)
  {
    return this;
  }

  public List<Localizable> getLocalizableRequirements()
  {
    List<Localizable> requirements = new ArrayList<>();

    if (getMinimumLength() > 0)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notLongEnough", getMinimumLength()));
    }

    if (getMinimumDigits() > 0)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notEnoughDigits", getMinimumDigits()));
    }

    if (getMinimumLowerCase() > 0)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notEnoughLowerCase", getMinimumLowerCase()));
    }

    if (getMinimumUpperCase() > 0)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notEnoughUpperCase", getMinimumUpperCase()));
    }

    if (getMinimumSpecial() > 0)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notEnoughSpecial", getMinimumSpecial()));
    }

    if (getMaximumLength() < Integer.MAX_VALUE)
    {
      requirements.add(LexiconText.toLocalizable("baja", "user.password.notShortEnough", getMaximumLength()));
    }

     return requirements;
  }

  /**
   * Convenience method to check that the given maximum password length (maxLen) cannot be less than
   * the given minimum password length (minLen) and also that the given maximum password length
   * cannot be less than the combined given minimum lowercase (lower), uppercase (upper), digits,
   * and special password characters required. Also, the minimum length, lowercase, uppercase,
   * digits, and special password characters cannot be less than zero and the maximum length cannot
   * be less than 1.
   *
   * @return true if the given password requirements are invalid, otherwise return false
   *
   * @since Niagara 4.13
   */
  private static boolean passwordRequirementsInvalid(int minLen, int maxLen, int lower,
                                                     int upper, int digits, int special)
  {
    return minLen < 0 || lower < 0 || upper < 0 || digits < 0 || special < 0 || maxLen < 1 ||
           minLen > maxLen || lower + upper + digits + special > maxLen;
  }

  public static final BPasswordStrength DEFAULT = new BPasswordStrength(PasswordStrength.DEFAULT);
  public static final BPasswordStrength FIPS_1 = new BPasswordStrength(PasswordStrength.FIPS_1);
  public static final BPasswordStrength STRONG = DEFAULT;
  public static final BPasswordStrength OFF     = new BPasswordStrength(0, 0, 0, 0, 0, Integer.MAX_VALUE);
}
